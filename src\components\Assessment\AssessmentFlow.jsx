import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { viaQuestions, riasecQuestions, bigFiveQuestions } from '../../data/assessmentQuestions';
import { ChevronLeft, ChevronRight, Save, RotateCcw } from 'lucide-react';

const AssessmentFlow = () => {
  const navigate = useNavigate();
  
  // State untuk assessment yang sedang aktif
  const [currentAssessment, setCurrentAssessment] = useState(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({
    via: {},
    riasec: {},
    bigFive: {}
  });
  const [isLoading, setIsLoading] = useState(false);

  // Load data dari localStorage saat komponen mount
  useEffect(() => {
    loadFromLocalStorage();
  }, []);

  // Save ke localStorage setiap kali answers berubah
  useEffect(() => {
    saveToLocalStorage();
  }, [answers, currentAssessment, currentQuestionIndex]);

  const loadFromLocalStorage = () => {
    try {
      const saved = localStorage.getItem('assessment_progress');
      if (saved) {
        const data = JSON.parse(saved);
        setAnswers(data.answers || { via: {}, riasec: {}, bigFive: {} });
        setCurrentAssessment(data.currentAssessment || null);
        setCurrentQuestionIndex(data.currentQuestionIndex || 0);
      }
    } catch (error) {
      console.error('Error loading from localStorage:', error);
    }
  };

  const saveToLocalStorage = () => {
    try {
      const data = {
        answers,
        currentAssessment,
        currentQuestionIndex,
        lastUpdated: new Date().toISOString()
      };
      localStorage.setItem('assessment_progress', JSON.stringify(data));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  };

  // Fungsi untuk mendapatkan data assessment yang sedang aktif
  const getAssessmentData = () => {
    switch (currentAssessment) {
      case 'via':
        return viaQuestions;
      case 'riasec':
        return riasecQuestions;
      case 'bigFive':
        return bigFiveQuestions;
      default:
        return null;
    }
  };

  // Fungsi untuk mendapatkan semua pertanyaan dalam format flat
  const getAllQuestions = (assessmentData) => {
    if (!assessmentData) return [];
    
    const questions = [];
    Object.entries(assessmentData.categories).forEach(([categoryKey, category]) => {
      // Regular questions
      if (category.questions) {
        category.questions.forEach((question, index) => {
          questions.push({
            id: `${categoryKey}_${index}`,
            text: question,
            category: categoryKey,
            categoryName: category.name,
            isReverse: false
          });
        });
      }
      
      // Reverse questions (untuk Big Five)
      if (category.reverseQuestions) {
        category.reverseQuestions.forEach((question, index) => {
          questions.push({
            id: `${categoryKey}_reverse_${index}`,
            text: question,
            category: categoryKey,
            categoryName: category.name,
            isReverse: true
          });
        });
      }
    });
    
    return questions;
  };

  const assessmentData = getAssessmentData();
  const allQuestions = getAllQuestions(assessmentData);
  const currentQuestion = allQuestions[currentQuestionIndex];

  // Fungsi untuk menangani jawaban
  const handleAnswer = (value) => {
    if (!currentQuestion) return;
    
    setAnswers(prev => ({
      ...prev,
      [currentAssessment]: {
        ...prev[currentAssessment],
        [currentQuestion.id]: value
      }
    }));
  };

  // Fungsi navigasi
  const goToNext = () => {
    if (currentQuestionIndex < allQuestions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const goToPrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  // Fungsi untuk memulai assessment
  const startAssessment = (type) => {
    setCurrentAssessment(type);
    setCurrentQuestionIndex(0);
  };

  // Fungsi untuk reset assessment
  const resetAssessment = () => {
    setCurrentAssessment(null);
    setCurrentQuestionIndex(0);
    setAnswers({ via: {}, riasec: {}, bigFive: {} });
    localStorage.removeItem('assessment_progress');
  };

  // Fungsi untuk menghitung progress
  const getProgress = () => {
    if (!currentAssessment || !allQuestions.length) return 0;
    const answered = Object.keys(answers[currentAssessment] || {}).length;
    return Math.round((answered / allQuestions.length) * 100);
  };

  // Jika belum memilih assessment, tampilkan pilihan
  if (!currentAssessment) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 py-8 px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-12"
          >
            <h1 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              AI-Driven Talent Mapping Assessment
            </h1>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              Pilih assessment yang ingin Anda kerjakan. Progress Anda akan tersimpan otomatis.
            </p>
          </motion.div>

          {/* Assessment Options */}
          <div className="grid md:grid-cols-3 gap-6 mb-8">
            {[
              {
                key: 'via',
                title: 'VIA Character Strengths',
                description: 'Mengidentifikasi kekuatan karakter Anda',
                questions: '96 pertanyaan',
                color: 'from-blue-500 to-blue-600'
              },
              {
                key: 'riasec',
                title: 'RIASEC Holland Codes',
                description: 'Mengeksplorasi minat karir Anda',
                questions: '60 pertanyaan',
                color: 'from-green-500 to-green-600'
              },
              {
                key: 'bigFive',
                title: 'Big Five Inventory',
                description: 'Menganalisis dimensi kepribadian Anda',
                questions: '44 pertanyaan',
                color: 'from-purple-500 to-purple-600'
              }
            ].map((assessment, index) => (
              <motion.div
                key={assessment.key}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden hover:shadow-md transition-shadow"
              >
                <div className={`h-2 bg-gradient-to-r ${assessment.color}`} />
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-slate-900 mb-2">
                    {assessment.title}
                  </h3>
                  <p className="text-slate-600 mb-4">
                    {assessment.description}
                  </p>
                  <p className="text-sm text-slate-500 mb-6">
                    {assessment.questions}
                  </p>
                  
                  {/* Progress indicator jika ada jawaban tersimpan */}
                  {Object.keys(answers[assessment.key] || {}).length > 0 && (
                    <div className="mb-4">
                      <div className="flex justify-between text-sm text-slate-600 mb-1">
                        <span>Progress</span>
                        <span>{Object.keys(answers[assessment.key]).length} jawaban tersimpan</span>
                      </div>
                      <div className="w-full bg-slate-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full bg-gradient-to-r ${assessment.color}`}
                          style={{ 
                            width: `${(Object.keys(answers[assessment.key]).length / 
                              (assessment.key === 'via' ? 96 : assessment.key === 'riasec' ? 60 : 44)) * 100}%` 
                          }}
                        />
                      </div>
                    </div>
                  )}
                  
                  <button
                    onClick={() => startAssessment(assessment.key)}
                    className={`w-full py-3 px-4 rounded-lg text-white font-medium bg-gradient-to-r ${assessment.color} hover:shadow-lg transition-all`}
                  >
                    {Object.keys(answers[assessment.key] || {}).length > 0 ? 'Lanjutkan' : 'Mulai Assessment'}
                  </button>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Reset Button */}
          {(Object.keys(answers.via).length > 0 || Object.keys(answers.riasec).length > 0 || Object.keys(answers.bigFive).length > 0) && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center"
            >
              <button
                onClick={resetAssessment}
                className="inline-flex items-center gap-2 px-4 py-2 text-slate-600 hover:text-slate-800 transition-colors"
              >
                <RotateCcw className="w-4 h-4" />
                Reset Semua Progress
              </button>
            </motion.div>
          )}
        </div>
      </div>
    );
  }

  // Jika sedang mengerjakan assessment
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 py-8 px-4">
      <div className="max-w-3xl mx-auto">
        {/* Header dengan progress */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl shadow-sm border border-slate-200 p-6 mb-6"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold text-slate-900">
                {assessmentData?.title}
              </h1>
              <p className="text-slate-600">
                Pertanyaan {currentQuestionIndex + 1} dari {allQuestions.length}
              </p>
            </div>
            <button
              onClick={() => setCurrentAssessment(null)}
              className="px-4 py-2 text-slate-600 hover:text-slate-800 transition-colors"
            >
              Kembali
            </button>
          </div>
          
          {/* Progress Bar */}
          <div className="w-full bg-slate-200 rounded-full h-3">
            <div 
              className="h-3 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300"
              style={{ width: `${((currentQuestionIndex + 1) / allQuestions.length) * 100}%` }}
            />
          </div>
          <div className="flex justify-between text-sm text-slate-600 mt-2">
            <span>Progress: {getProgress()}% jawaban tersimpan</span>
            <span>{Math.round(((currentQuestionIndex + 1) / allQuestions.length) * 100)}% selesai</span>
          </div>
        </motion.div>

        {/* Question Card */}
        <AnimatePresence mode="wait">
          {currentQuestion && (
            <motion.div
              key={currentQuestion.id}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="bg-white rounded-xl shadow-sm border border-slate-200 p-8"
            >
              {/* Category Badge */}
              <div className="inline-block px-3 py-1 bg-slate-100 text-slate-700 text-sm rounded-full mb-6">
                {currentQuestion.categoryName}
              </div>
              
              {/* Question */}
              <h2 className="text-xl font-semibold text-slate-900 mb-8 leading-relaxed">
                {currentQuestion.text}
              </h2>
              
              {/* Scale */}
              <div className="space-y-3 mb-8">
                {assessmentData.scale.map((option) => (
                  <label
                    key={option.value}
                    className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all ${
                      answers[currentAssessment]?.[currentQuestion.id] === option.value
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-slate-200 hover:border-slate-300'
                    }`}
                  >
                    <input
                      type="radio"
                      name="answer"
                      value={option.value}
                      checked={answers[currentAssessment]?.[currentQuestion.id] === option.value}
                      onChange={() => handleAnswer(option.value)}
                      className="sr-only"
                    />
                    <div className={`w-5 h-5 rounded-full border-2 mr-4 flex items-center justify-center ${
                      answers[currentAssessment]?.[currentQuestion.id] === option.value
                        ? 'border-blue-500 bg-blue-500'
                        : 'border-slate-300'
                    }`}>
                      {answers[currentAssessment]?.[currentQuestion.id] === option.value && (
                        <div className="w-2 h-2 rounded-full bg-white" />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-slate-900">{option.label}</span>
                        <span className="text-slate-500 text-sm">{option.value}</span>
                      </div>
                    </div>
                  </label>
                ))}
              </div>
              
              {/* Navigation */}
              <div className="flex justify-between items-center">
                <button
                  onClick={goToPrevious}
                  disabled={currentQuestionIndex === 0}
                  className="inline-flex items-center gap-2 px-6 py-3 text-slate-600 hover:text-slate-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <ChevronLeft className="w-4 h-4" />
                  Sebelumnya
                </button>
                
                <div className="flex items-center gap-2 text-sm text-slate-500">
                  <Save className="w-4 h-4" />
                  Tersimpan otomatis
                </div>
                
                <button
                  onClick={goToNext}
                  disabled={currentQuestionIndex === allQuestions.length - 1}
                  className="inline-flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Selanjutnya
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default AssessmentFlow;
