import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { CheckCircle, Clock, AlertCircle, ArrowLeft } from 'lucide-react';
import EnhancedLoadingScreen from '../UI/EnhancedLoadingScreen';

const AssessmentStatus = () => {
  const { jobId } = useParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState('processing');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulasi status check - nanti bisa diganti dengan API call
    const timer = setTimeout(() => {
      setLoading(false);
      // Untuk demo, set status completed setelah 3 detik
      setTimeout(() => {
        setStatus('completed');
      }, 3000);
    }, 1000);

    return () => clearTimeout(timer);
  }, [jobId]);

  if (loading) {
    return <EnhancedLoadingScreen />;
  }

  const getStatusConfig = () => {
    switch (status) {
      case 'processing':
        return {
          icon: Clock,
          title: 'Memproses Assessment',
          description: 'AI sedang menganalisis hasil assessment Anda...',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200'
        };
      case 'completed':
        return {
          icon: CheckCircle,
          title: 'Assessment Selesai',
          description: 'Hasil assessment Anda telah siap untuk dilihat.',
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200'
        };
      case 'failed':
        return {
          icon: AlertCircle,
          title: 'Assessment Gagal',
          description: 'Terjadi kesalahan saat memproses assessment Anda.',
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200'
        };
      default:
        return {
          icon: Clock,
          title: 'Status Tidak Diketahui',
          description: 'Sedang memeriksa status assessment...',
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200'
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 py-8 px-4">
      <div className="max-w-2xl mx-auto">
        {/* Back Button */}
        <motion.button
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          onClick={() => navigate('/dashboard')}
          className="inline-flex items-center gap-2 text-slate-600 hover:text-slate-800 mb-8 transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          Kembali ke Dashboard
        </motion.button>

        {/* Status Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className={`bg-white rounded-xl shadow-sm border-2 ${config.borderColor} p-8 text-center`}
        >
          {/* Status Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className={`inline-flex items-center justify-center w-20 h-20 rounded-full ${config.bgColor} mb-6`}
          >
            <Icon className={`w-10 h-10 ${config.color}`} />
          </motion.div>

          {/* Status Title */}
          <motion.h1
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="text-2xl font-bold text-slate-900 mb-4"
          >
            {config.title}
          </motion.h1>

          {/* Status Description */}
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="text-slate-600 mb-8 text-lg"
          >
            {config.description}
          </motion.p>

          {/* Job ID */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="bg-slate-50 rounded-lg p-4 mb-8"
          >
            <p className="text-sm text-slate-500 mb-1">Job ID</p>
            <p className="font-mono text-slate-800">{jobId}</p>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="space-y-4"
          >
            {status === 'completed' && (
              <button
                onClick={() => navigate('/dashboard')}
                className="w-full py-3 px-6 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
              >
                Lihat Hasil Assessment
              </button>
            )}
            
            {status === 'failed' && (
              <div className="space-y-3">
                <button
                  onClick={() => navigate('/assessment')}
                  className="w-full py-3 px-6 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  Coba Lagi
                </button>
                <button
                  onClick={() => navigate('/dashboard')}
                  className="w-full py-3 px-6 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors font-medium"
                >
                  Kembali ke Dashboard
                </button>
              </div>
            )}
            
            {status === 'processing' && (
              <div className="space-y-3">
                <div className="flex items-center justify-center gap-2 text-slate-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span className="text-sm">Estimasi waktu: 2-3 menit</span>
                </div>
                <button
                  onClick={() => navigate('/dashboard')}
                  className="w-full py-3 px-6 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors font-medium"
                >
                  Tunggu di Dashboard
                </button>
              </div>
            )}
          </motion.div>
        </motion.div>

        {/* Additional Info */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
          className="mt-8 text-center"
        >
          <p className="text-sm text-slate-500">
            Anda akan menerima notifikasi ketika assessment selesai diproses.
          </p>
        </motion.div>
      </div>
    </div>
  );
};

export default AssessmentStatus;
